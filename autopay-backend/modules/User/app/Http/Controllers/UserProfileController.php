<?php

namespace Modules\User\Http\Controllers;

use Illuminate\Http\Request;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Models\Organization;
use Modules\Team\Models\Team;
use Modules\User\Data\UserData;
use Modules\User\Events\UserDeleted;
use Modules\User\Http\Requests\DeleteProfileRequest;
use Modules\User\Http\Requests\UpdateProfileRequest;
use Modules\User\Models\DeletedUser;
use Modules\User\Models\PersonalAccessToken;
use Modules\User\Models\SocialAccount;
use Modules\User\Data\SocialAccountData;
use stdClass;
use Symfony\Component\HttpFoundation\Response;

class UserProfileController
{
    /**
     * Get user profile with organizations and teams
     */
    public function profile(): Response
    {
        $userData = UserData::fromUser(auth()->user());

        return ResponseHelper::success(data: $userData);
    }

    public function updateProfile(UpdateProfileRequest $request): Response
    {
        $user = $request->user();

        // Handle current team switching (organization-based)
        if ($request->filled('current_team_id')) {
            $teamId = $request->input('current_team_id');
            $team = Team::find($teamId);

            if (! $team) {
                return ResponseHelper::error('Nhóm không tồn tại.');
            }

            // Check if a user belongs to this team in any organization
            $belongsToTeam = false;
            foreach ($user->activeOrganizations as $organization) {
                if ($user->belongsToTeam($organization, $team)) {
                    $belongsToTeam = true;
                    break;
                }
            }

            if (! $belongsToTeam) {
                return ResponseHelper::error('Bạn không phải là thành viên của nhóm này.');
            }

            // Update current team
            $user->update(['current_team_id' => $teamId]);

        } elseif ($request->filled('timezone')) {
            $user->data->timezone = $request->input('timezone');
            $user->save();
        } else {
            $user->update($request->only(['name', 'email']));
        }

        return ResponseHelper::success('Hồ sơ của bạn đã được cập nhật.');
    }

    public function deleteProfile(DeleteProfileRequest $request): Response
    {
        // Get all servers belongs to this current profile
        $user = $request->user();
        $servers = $user->servers()->get();

        $servers->map(function ($server): void {
            //            $this->deleteServer($server);
        });

        // MD5 (Email) to check later if another one using this email for account registering
        DeletedUser::updateOrCreate([
            'email_hashed' => md5((string) $user->email),
        ], [
            'updated_at' => now(),
        ]);

        // ! Schedule an event to send email, must clone and convert to array because queue cannot re-product model data after deleted
        UserDeleted::dispatch($user);

        return ResponseHelper::success('Hồ sơ của bạn đã được xóa.');
    }

    /**
     * Get user's activity/notifications (placeholder)
     */
    public function activity(Request $request): Response
    {
        //        $perPage = min($request->query('per_page', 10), 50);

        // Placeholder for an activity system
        $activities = collect();

        return ResponseHelper::success(
            data: [
                'activities' => $activities,
                'unread_count' => 0,
            ]
        );
    }

    /**
     * Update user preferences
     */
    public function updatePreferences(Request $request): Response
    {
        $user = auth()->user();

        $validatedData = $request->validate([
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
            'notifications' => 'array',
            'notifications.email' => 'boolean',
            'notifications.browser' => 'boolean',
            'notifications.mobile' => 'boolean',
        ]);

        // Update user data/preferences
        $userData = $user->data ?? new stdClass;

        if (isset($validatedData['timezone'])) {
            $userData->timezone = $validatedData['timezone'];
        }

        if (isset($validatedData['language'])) {
            $userData->language = $validatedData['language'];
        }

        if (isset($validatedData['notifications'])) {
            $userData->notifications = $validatedData['notifications'];
        }

        $user->update(['data' => $userData]);

        return ResponseHelper::success(
            message: 'Cập nhật tùy chọn thành công',
            data: $userData
        );
    }

    /**
     * Get user's social accounts
     */
    public function socialAccounts(): Response
    {
        $user = auth()->user();
        $socialAccounts = $user->socialAccount()->get();

        // Use Laravel Data collect method to transform collection
        $socialAccountsData = SocialAccountData::collect($socialAccounts);

        return ResponseHelper::success(data: $socialAccountsData);
    }

    /**
     * Delete a social account
     */
    public function deleteSocialAccount(SocialAccount $socialAccount): Response
    {
        $user = auth()->user();

        // Check if the social account belongs to the current user
        if ($socialAccount->user_id !== $user->id) {
            return ResponseHelper::error('Bạn không có quyền xóa tài khoản này.');
        }

        $socialAccount->delete();

        return ResponseHelper::success('Tài khoản đã được ngắt kết nối thành công.');
    }

    /**
     * Connect a social account to current user
     */
    public function connectSocialAccount(Request $request, string $provider): Response
    {
        $user = auth()->user();

        // Validate provider
        if (!in_array($provider, ['google'])) {
            return ResponseHelper::error('Nhà cung cấp không được hỗ trợ.');
        }

        // Validate required fields
        $validatedData = $request->validate([
            'oauth.providerAccountId' => 'required|string',
            'name' => 'required|string|max:255',
            'image' => 'nullable|string',
        ]);

        $providerAccountId = $validatedData['oauth']['providerAccountId'];
        $name = $validatedData['name'];
        $avatar = $validatedData['image'] ?? '';

        // Check if this social account is already connected to any user
        $existingSocialAccount = SocialAccount::whereAccountId($providerAccountId)
            ->whereProvider($provider)
            ->first();

        if ($existingSocialAccount) {
            if ($existingSocialAccount->user_id === $user->id) {
                return ResponseHelper::error('Tài khoản này đã được kết nối với tài khoản của bạn.');
            } else {
                return ResponseHelper::error('Tài khoản này đã được kết nối với tài khoản khác trong hệ thống.');
            }
        }

        // Create new social account connection
        $user->socialAccount()->create([
            'account_id' => $providerAccountId,
            'provider' => $provider,
            'name' => $name,
            'avatar' => strlen($avatar) < 255 ? $avatar : '', // Google avatar is too long
        ]);

        return ResponseHelper::success('Kết nối tài khoản thành công.');
    }

    /**
     * Get user's personal access tokens
     */
    public function tokens(): Response
    {
        $user = auth()->user();
        $currentToken = request()->bearerToken();
        $currentTokenHash = $currentToken ? hash('sha256', $currentToken) : null;

        $tokens = $user->tokens()
            ->select(['id', 'name', 'abilities', 'last_used_at', 'created_at', 'expires_at', 'token'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($token) use ($currentTokenHash) {
                return [
                    'id' => $token->id,
                    'name' => $token->name,
                    'last_used_at' => $token->last_used_at?->format('Y-m-d H:i:s'),
                    'created_at' => $token->created_at->format('Y-m-d H:i:s'),
                    'expires_at' => $token->expires_at?->format('Y-m-d H:i:s'),
                    'is_current' => $currentTokenHash && $token->token === $currentTokenHash,
                ];
            });

        return ResponseHelper::success(data: ['items' => $tokens]);
    }

    /**
     * Delete a specific personal access token
     */
    public function deleteToken(string $tokenId): Response
    {
        $user = auth()->user();
        $currentToken = request()->bearerToken();
        $currentTokenHash = $currentToken ? hash('sha256', $currentToken) : null;

        $token = PersonalAccessToken::where('id', $tokenId)
            ->where('tokenable_id', $user->id)
            ->where('tokenable_type', get_class($user))
            ->first();

        if (!$token) {
            return ResponseHelper::error('Token không tồn tại.');
        }

        // Prevent deleting current token
        if ($currentTokenHash && $token->token === $currentTokenHash) {
            return ResponseHelper::error('Không thể xóa phiên đăng nhập hiện tại.');
        }

        $token->delete();

        return ResponseHelper::success('Token đã được xóa thành công.');
    }
}
