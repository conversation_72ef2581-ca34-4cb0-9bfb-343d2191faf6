'use client'

import { DataTable } from '@/components/custom-ui/data-table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { formatDistanceToNow } from 'date-fns'
import { vi } from 'date-fns/locale'
import { MoreHorizontal, Trash2 } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'

interface Token {
  id: string
  name: string
  last_used_at: string | null
  created_at: string
  expires_at: string | null
  is_current: boolean
}

export default function Component() {
  const [deletingTokenId, setDeletingTokenId] = useState<string | null>(null)
  const queryClient = useQueryClient()

  const { data, isLoading } = useQuery({
    queryKey: ['profile-tokens'],
    queryFn: () => queryFetchHelper('/profile/tokens'),
  })

  const tokens: Token[] = data?.data?.items || []

  const deleteTokenMutation = useMutation({
    mutationFn: (tokenId: string) => queryFetchHelper(`/profile/tokens/${tokenId}`, { method: 'DELETE' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile-tokens'] })
      toast.success('Token đã được xóa thành công')
      setDeletingTokenId(null)
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi xóa token')
      setDeletingTokenId(null)
    },
  })

  const handleDeleteToken = (tokenId: string): void => {
    setDeletingTokenId(tokenId)
    deleteTokenMutation.mutate(tokenId)
  }

  const getDeviceName = (userAgent: string): string => {
    if (userAgent.includes('Chrome')) {
      if (userAgent.includes('Macintosh')) return 'Chrome trên macOS'
      if (userAgent.includes('Windows')) return 'Chrome trên Windows'
      if (userAgent.includes('Linux')) return 'Chrome trên Linux'
      return 'Chrome'
    }
    if (userAgent.includes('Firefox')) return 'Firefox'
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari'
    if (userAgent.includes('Edge')) return 'Microsoft Edge'
    return 'Trình duyệt khác'
  }

  const columns: ColumnDef<Token>[] = [
    {
      accessorKey: 'name',
      header: 'Thiết bị / Trình duyệt',
      cell: ({ row }) => {
        const token = row.original
        const deviceName = getDeviceName(token.name)
        return (
          <div className="flex flex-col">
            <div className="font-medium">{deviceName}</div>
            {token.is_current && (
              <Badge
                variant="secondary"
                className="mt-1 w-fit">
                Phiên hiện tại
              </Badge>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: 'last_used_at',
      header: 'Lần cuối sử dụng',
      cell: ({ row }) => {
        const lastUsed = row.getValue('last_used_at') as string | null
        if (!lastUsed) return <span className="text-muted-foreground">Chưa sử dụng</span>
        return (
          <span className="text-sm">{formatDistanceToNow(new Date(lastUsed), { addSuffix: true, locale: vi })}</span>
        )
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Ngày tạo',
      cell: ({ row }) => {
        const createdAt = row.getValue('created_at') as string
        return (
          <span className="text-sm">{formatDistanceToNow(new Date(createdAt), { addSuffix: true, locale: vi })}</span>
        )
      },
    },
    {
      accessorKey: 'expires_at',
      header: 'Ngày hết hạn',
      cell: ({ row }) => {
        const expiresAt = row.getValue('expires_at') as string | null
        if (!expiresAt) {
          return <span className="text-muted-foreground">Không giới hạn</span>
        }
        return (
          <span className="text-sm">{formatDistanceToNow(new Date(expiresAt), { addSuffix: true, locale: vi })}</span>
        )
      },
    },
    {
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const token = row.original

        if (token.is_current) {
          return null // Don't show actions for current token
        }

        return (
          <div className="flex justify-end">
            <AlertDialog>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem className="cursor-pointer text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Xóa token
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                </DropdownMenuContent>
              </DropdownMenu>

              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Xác nhận xóa token</AlertDialogTitle>
                  <AlertDialogDescription>
                    Bạn có chắc chắn muốn xóa token này? Hành động này không thể hoàn tác và sẽ đăng xuất thiết bị tương
                    ứng.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Hủy</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeleteToken(token.id)}
                    disabled={deletingTokenId === token.id}
                    className="bg-red-600 hover:bg-red-700">
                    {deletingTokenId === token.id ? 'Đang xóa...' : 'Xóa token'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )
      },
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Phiên đăng nhập</h3>
        <p className="text-muted-foreground text-sm">
          Quản lý các phiên đăng nhập của bạn trên các thiết bị khác nhau.
        </p>
      </div>

      <DataTable
        columns={columns}
        data={tokens}
        isLoading={isLoading}
        emptyMessage="Không có phiên đăng nhập nào."
      />
    </div>
  )
}
